-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local UserInputService = _services.UserInputService
local RunService = _services.RunService
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local ActionBar = _core.ActionBar
local ToastService = _core.ToastService
local safeCall = _core.safeCall
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local ABILITIES = TS.import(script, game:GetService("ReplicatedStorage"), "TS", "abilities", "AbilityTypes").ABILITIES
local RoomAbility = TS.import(script, script.Parent.Parent, "abilities", "RoomAbility").RoomAbility
local QuakeAbility = TS.import(script, script.Parent.Parent, "abilities", "whitebeard", "QuakeAbility").QuakeAbility
local HakiDominanceAbility = TS.import(script, script.Parent.Parent, "abilities", "HakiDominanceAbility").HakiDominanceAbility
local IceAgeAbility = TS.import(script, script.Parent.Parent, "abilities", "IceAgeAbility").IceAgeAbility
local FireFistAbility = TS.import(script, script.Parent.Parent, "abilities", "FireFistAbility").FireFistAbility
local ThreeSwordStyleAbility = TS.import(script, script.Parent.Parent, "abilities", "ThreeSwordStyleAbility").ThreeSwordStyleAbility
local function ActionBarDemo(props)
	-- Debug: Log when ActionBarDemo renders
	print("🔥 ActionBarDemo is rendering!")
	-- State to track cooldowns for each ability
	local cooldowns, setCooldowns = React.useState({})
	-- State to track if abilities are initialized
	local abilitiesInitialized, setAbilitiesInitialized = React.useState(false)
	-- Force re-render to update cooldown states
	local _, forceUpdate = React.useReducer(function(x)
		return x + 1
	end, 0)
	-- State for QuakeAbility punch type selection
	local selectedPunchType, setSelectedPunchType = React.useState("single")
	-- Initialize abilities as singletons to avoid duplication
	local roomAbility = React.useRef()
	local quakeAbility = React.useRef()
	local hakiDominanceAbility = React.useRef()
	local iceAgeAbility = React.useRef()
	local fireFistAbility = React.useRef()
	local threeSwordStyleAbility = React.useRef()
	-- Initialize abilities only once with error handling
	React.useEffect(function()
		local initializeAbilities = function()
			local allInitialized = true
			if not roomAbility.current then
				local instance = safeCall(function()
					return RoomAbility.new()
				end, "ActionBarDemo", "initialize_RoomAbility", nil)
				if instance then
					roomAbility.current = instance
					print("✅ RoomAbility initialized successfully")
				else
					warn("❌ Failed to initialize RoomAbility")
					allInitialized = false
				end
			end
			if not quakeAbility.current then
				local instance = safeCall(function()
					return QuakeAbility.new()
				end, "ActionBarDemo", "initialize_QuakeAbility", nil)
				if instance then
					quakeAbility.current = instance
					print("✅ QuakeAbility initialized successfully")
				else
					warn("❌ Failed to initialize QuakeAbility")
					allInitialized = false
				end
			end
			if not hakiDominanceAbility.current then
				local instance = safeCall(function()
					return HakiDominanceAbility.new()
				end, "ActionBarDemo", "initialize_HakiDominanceAbility", nil)
				if instance then
					hakiDominanceAbility.current = instance
					print("✅ HakiDominanceAbility initialized successfully")
				else
					warn("❌ Failed to initialize HakiDominanceAbility")
					allInitialized = false
				end
			end
			if not iceAgeAbility.current then
				local instance = safeCall(function()
					return IceAgeAbility.new()
				end, "ActionBarDemo", "initialize_IceAgeAbility", nil)
				if instance then
					iceAgeAbility.current = instance
					print("✅ IceAgeAbility initialized successfully")
				else
					warn("❌ Failed to initialize IceAgeAbility")
					allInitialized = false
				end
			end
			if not fireFistAbility.current then
				local instance = safeCall(function()
					return FireFistAbility.new()
				end, "ActionBarDemo", "initialize_FireFistAbility", nil)
				if instance then
					fireFistAbility.current = instance
					print("✅ FireFistAbility initialized successfully")
				else
					warn("❌ Failed to initialize FireFistAbility")
					allInitialized = false
				end
			end
			if not threeSwordStyleAbility.current then
				local instance = safeCall(function()
					return ThreeSwordStyleAbility.new()
				end, "ActionBarDemo", "initialize_ThreeSwordStyleAbility", nil)
				if instance then
					threeSwordStyleAbility.current = instance
					print("✅ ThreeSwordStyleAbility initialized successfully")
				else
					warn("❌ Failed to initialize ThreeSwordStyleAbility")
					allInitialized = false
				end
			end
			return allInitialized
		end
		local success = initializeAbilities()
		setAbilitiesInitialized(success)
		if success then
			print("✅ All abilities initialized successfully")
		else
			warn("⚠️ Some abilities failed to initialize")
		end
	end, {})
	-- Clean up expired cooldowns and force re-render
	React.useEffect(function()
		-- Only run the effect if there are active cooldowns
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _1 in cooldowns do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		if _size == 0 then
			return nil
		end
		-- Check every 100ms instead of every frame for better performance
		local lastCheckTime = tick()
		local checkInterval = 0.1
		local connection = RunService.Heartbeat:Connect(function()
			local currentTime = tick()
			-- Only check every 100ms
			if currentTime - lastCheckTime < checkInterval then
				return nil
			end
			lastCheckTime = currentTime
			local hasExpiredCooldowns = false
			setCooldowns(function(prev)
				-- If no cooldowns, nothing to update
				-- ▼ ReadonlyMap.size ▼
				local _size_1 = 0
				for _1 in prev do
					_size_1 += 1
				end
				-- ▲ ReadonlyMap.size ▲
				if _size_1 == 0 then
					return prev
				end
				local newMap = {}
				-- ▼ ReadonlyMap.forEach ▼
				local _callback = function(endTime, abilityId)
					if currentTime < endTime then
						-- Cooldown still active
						local _abilityId = abilityId
						local _endTime = endTime
						newMap[_abilityId] = _endTime
					else
						-- Cooldown expired
						hasExpiredCooldowns = true
						print(`✅ Cooldown for {abilityId} has expired!`)
					end
				end
				for _k, _v in prev do
					_callback(_v, _k, prev)
				end
				-- ▲ ReadonlyMap.forEach ▲
				return newMap
			end)
			if hasExpiredCooldowns then
				forceUpdate()
			end
		end)
		return function()
			return connection:Disconnect()
		end
	end, { cooldowns, forceUpdate })
	-- Get current punch type display info
	local getPunchTypeInfo = function(punchType)
		return if punchType == "single" then {
			text = "Single Punch",
			icon = "👊",
		} else {
			text = "Cross Punch",
			icon = "🥊",
		}
	end
	local currentPunchInfo = getPunchTypeInfo(selectedPunchType)
	-- Sample abilities for the action bar (5 slots)
	local _object = {
		id = "ROOM",
		name = ABILITIES.ROOM.name,
		icon = "🔵",
		cooldownEndTime = cooldowns.ROOM,
	}
	local _left = "isOnCooldown"
	local _condition = cooldowns.ROOM ~= nil
	if _condition then
		local _exp = tick()
		local _condition_1 = cooldowns.ROOM
		if _condition_1 == nil then
			_condition_1 = 0
		end
		_condition = _exp < _condition_1
	end
	_object[_left] = _condition
	_object.totalCooldownDuration = ABILITIES.ROOM.cooldown
	local _object_1 = {
		id = "QUAKE_PUNCH",
		name = `{ABILITIES.QUAKE_PUNCH.name} ({currentPunchInfo.text})`,
		icon = currentPunchInfo.icon,
		cooldownEndTime = cooldowns.QUAKE_PUNCH,
	}
	local _left_1 = "isOnCooldown"
	local _condition_1 = cooldowns.QUAKE_PUNCH ~= nil
	if _condition_1 then
		local _exp = tick()
		local _condition_2 = cooldowns.QUAKE_PUNCH
		if _condition_2 == nil then
			_condition_2 = 0
		end
		_condition_1 = _exp < _condition_2
	end
	_object_1[_left_1] = _condition_1
	_object_1.totalCooldownDuration = ABILITIES.QUAKE_PUNCH.cooldown
	local _object_2 = {
		id = "ICE_AGE",
		name = ABILITIES.ICE_AGE.name,
		icon = "❄️",
		cooldownEndTime = cooldowns.ICE_AGE,
	}
	local _left_2 = "isOnCooldown"
	local _condition_2 = cooldowns.ICE_AGE ~= nil
	if _condition_2 then
		local _exp = tick()
		local _condition_3 = cooldowns.ICE_AGE
		if _condition_3 == nil then
			_condition_3 = 0
		end
		_condition_2 = _exp < _condition_3
	end
	_object_2[_left_2] = _condition_2
	_object_2.totalCooldownDuration = ABILITIES.ICE_AGE.cooldown
	local _object_3 = {
		id = "FIRE_FIST",
		name = ABILITIES.FIRE_FIST.name,
		icon = "🔥",
		cooldownEndTime = cooldowns.FIRE_FIST,
	}
	local _left_3 = "isOnCooldown"
	local _condition_3 = cooldowns.FIRE_FIST ~= nil
	if _condition_3 then
		local _exp = tick()
		local _condition_4 = cooldowns.FIRE_FIST
		if _condition_4 == nil then
			_condition_4 = 0
		end
		_condition_3 = _exp < _condition_4
	end
	_object_3[_left_3] = _condition_3
	_object_3.totalCooldownDuration = ABILITIES.FIRE_FIST.cooldown
	local _object_4 = {
		id = "THREE_SWORD_STYLE",
		name = ABILITIES.THREE_SWORD_STYLE.name,
		icon = "⚔️",
		cooldownEndTime = cooldowns.THREE_SWORD_STYLE,
	}
	local _left_4 = "isOnCooldown"
	local _condition_4 = cooldowns.THREE_SWORD_STYLE ~= nil
	if _condition_4 then
		local _exp = tick()
		local _condition_5 = cooldowns.THREE_SWORD_STYLE
		if _condition_5 == nil then
			_condition_5 = 0
		end
		_condition_4 = _exp < _condition_5
	end
	_object_4[_left_4] = _condition_4
	_object_4.totalCooldownDuration = ABILITIES.THREE_SWORD_STYLE.cooldown
	local abilitySlots = { _object, _object_1, _object_2, _object_3, _object_4 }
	local handleSlotClick = function(slotIndex, abilityId)
		local ability = ABILITIES[abilityId]
		if not ability then
			print(`Unknown ability: {abilityId}`)
			return nil
		end
		-- Check if ability is on cooldown
		local currentTime = tick()
		local _abilityId = abilityId
		local cooldownEndTime = cooldowns[_abilityId]
		if cooldownEndTime ~= nil and cooldownEndTime > 0 and currentTime < cooldownEndTime then
			local remainingTime = math.ceil(cooldownEndTime - currentTime)
			print(`{ability.name} is on cooldown! {remainingTime} seconds remaining.`)
			ToastService:showWarning("Ability on Cooldown", `{ability.name} - {remainingTime}s remaining`)
			return nil
		end
		-- Special handling for specific abilities with safety checks
		local _exitType, _returns = TS.try(function()
			if abilityId == "ROOM" then
				if roomAbility.current then
					roomAbility.current:activate()
					print(`🔵 Casting {ability.name} from slot {slotIndex + 1}!`)
					ToastService:showSuccess("Room Activated", "Spatial manipulation field created")
				else
					warn("❌ RoomAbility not initialized")
					ToastService:showError("Error", "Room ability not ready")
					return TS.TRY_RETURN, {}
				end
			elseif abilityId == "QUAKE_PUNCH" then
				if not quakeAbility.current then
					warn("❌ QuakeAbility not initialized")
					ToastService:showError("Error", "Quake ability not ready")
					return TS.TRY_RETURN, {}
				end
				-- Check if shift is held to cycle punch type
				local isShiftHeld = UserInputService:IsKeyDown(Enum.KeyCode.LeftShift) or UserInputService:IsKeyDown(Enum.KeyCode.RightShift)
				if isShiftHeld then
					-- Cycle punch type instead of activating
					local newType = if selectedPunchType == "single" then "double" else "single"
					setSelectedPunchType(newType)
					local newInfo = getPunchTypeInfo(newType)
					print(`🔄 Switched to {newInfo.text} {newInfo.icon}`)
					ToastService:showInfo("Punch Type Changed", `Now using: {newInfo.text}`)
					return TS.TRY_RETURN, {}
				else
					-- Activate with current punch type
					quakeAbility.current:activate(selectedPunchType)
					print(`{currentPunchInfo.icon} Casting {ability.name} ({currentPunchInfo.text}) from slot {slotIndex + 1}!`)
					ToastService:showSuccess(`{currentPunchInfo.icon} Quake Punch`, `{currentPunchInfo.text} unleashed!`)
				end
			elseif abilityId == "THREE_SWORD_STYLE" then
				if threeSwordStyleAbility.current then
					threeSwordStyleAbility.current:activate()
					print(`⚔️ Casting {ability.name} - Zoro's legendary sword technique from slot {slotIndex + 1}!`)
					ToastService:showSuccess("⚔️ Three Sword Style", "Santoryu technique activated!")
				else
					warn("❌ ThreeSwordStyleAbility not initialized")
					ToastService:showError("Error", "Three Sword Style ability not ready")
					return TS.TRY_RETURN, {}
				end
			elseif abilityId == "ICE_AGE" then
				if iceAgeAbility.current then
					iceAgeAbility.current:activate()
					print(`❄️ Casting {ability.name} - Aokiji's ultimate ice technique from slot {slotIndex + 1}!`)
					ToastService:showSuccess("❄️ Ice Age", "Freezing the battlefield...")
				else
					warn("❌ IceAgeAbility not initialized")
					ToastService:showError("Error", "Ice Age ability not ready")
					return TS.TRY_RETURN, {}
				end
			elseif abilityId == "FIRE_FIST" then
				if fireFistAbility.current then
					fireFistAbility.current:activate()
					print(`🔥 Casting {ability.name} - Ace's signature fire attack from slot {slotIndex + 1}!`)
					ToastService:showSuccess("🔥 Fire Fist", "Ace's burning attack unleashed!")
				else
					warn("❌ FireFistAbility not initialized")
					ToastService:showError("Error", "Fire Fist ability not ready")
					return TS.TRY_RETURN, {}
				end
			elseif abilityId == "HAKI_DOMINANCE" then
				if hakiDominanceAbility.current then
					hakiDominanceAbility.current:activate()
					print(`🔴 Casting {ability.name} - Rayleigh's overwhelming Haki from slot {slotIndex + 1}!`)
					ToastService:showSuccess("🔴 Haki Dominance", "Overwhelming presence activated!")
				else
					warn("❌ HakiDominanceAbility not initialized")
					ToastService:showError("Error", "Haki Dominance ability not ready")
					return TS.TRY_RETURN, {}
				end
			else
				-- Simulate ability cast for other abilities
				print(`🔥 Casting {ability.name} from slot {slotIndex + 1}!`)
				ToastService:showSuccess("Ability Activated", ability.name)
			end
			-- Set cooldown
			local newCooldownEndTime = currentTime + ability.cooldown
			setCooldowns(function(prev)
				local newMap = {}
				-- Copy existing entries
				-- ▼ ReadonlyMap.forEach ▼
				local _callback = function(value, key)
					local _key = key
					local _value = value
					newMap[_key] = _value
				end
				for _k, _v in prev do
					_callback(_v, _k, prev)
				end
				-- ▲ ReadonlyMap.forEach ▲
				-- Set new cooldown
				local _abilityId_1 = abilityId
				newMap[_abilityId_1] = newCooldownEndTime
				return newMap
			end)
		end, function(error)
			warn(`❌ Error activating ability {abilityId}: {error}`)
			ToastService:showError("Ability Error", `Failed to activate {ability.name}`)
		end)
		if _exitType then
			return unpack(_returns)
		end
	end
	-- Handle keyboard input for ability slots (1, 2, 3, 4, 5)
	React.useEffect(function()
		local connection = UserInputService.InputBegan:Connect(function(input, gameProcessed)
			if gameProcessed then
				return nil
			end
			if input.UserInputType == Enum.UserInputType.Keyboard then
				local keyCode = input.KeyCode
				local slotIndex = -1
				-- Map number keys to slot indices
				if keyCode == Enum.KeyCode.One then
					slotIndex = 0
				elseif keyCode == Enum.KeyCode.Two then
					slotIndex = 1
				elseif keyCode == Enum.KeyCode.Three then
					slotIndex = 2
				elseif keyCode == Enum.KeyCode.Four then
					slotIndex = 3
				elseif keyCode == Enum.KeyCode.Five then
					slotIndex = 4
				end
				-- Trigger ability if valid slot
				if slotIndex >= 0 and slotIndex < #abilitySlots then
					local slot = abilitySlots[slotIndex + 1]
					handleSlotClick(slotIndex, slot.id)
				end
			end
		end)
		return function()
			return connection:Disconnect()
		end
	end, { abilitySlots, handleSlotClick, selectedPunchType })
	-- Get responsive manager for dynamic sizing
	local responsiveManager = ResponsiveManager:getInstance()
	local safeAreaInsets = responsiveManager:getSafeAreaInsets()
	-- Calculate responsive container size and position
	local containerWidth = if responsiveManager:isMobile() then 320 else 400
	local containerHeight = if responsiveManager:isMobile() then 70 else 80
	-- Reduce the bottom margin - the original was 120px from bottom, let's use a smaller base margin
	local baseBottomMargin = if responsiveManager:isMobile() then 80 else 100
	local bottomMargin = baseBottomMargin + (if responsiveManager:isMobile() then safeAreaInsets.bottom * 0.5 else 0)
	-- Don't render the action bar until abilities are initialized
	if not abilitiesInitialized then
		return React.createElement("frame", {
			BackgroundTransparency = 1,
			Size = UDim2.new(1, 0, 1, 0),
			Position = UDim2.new(0, 0, 0, 0),
			BorderSizePixel = 0,
		}, React.createElement("textlabel", {
			Text = "Initializing abilities...",
			Size = UDim2.new(0, 200, 0, 30),
			Position = UDim2.new(0.5, 0, 1, -50),
			AnchorPoint = Vector2.new(0.5, 0),
			BackgroundTransparency = 1,
			TextColor3 = Color3.fromRGB(255, 255, 255),
			TextSize = 14,
			Font = Enum.Font.SourceSans,
			TextXAlignment = Enum.TextXAlignment.Center,
		}))
	end
	return React.createElement("frame", {
		BackgroundTransparency = 1,
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BorderSizePixel = 0,
	}, React.createElement("frame", {
		BackgroundTransparency = 1,
		Size = UDim2.new(0, containerWidth, 0, containerHeight),
		Position = UDim2.new(0.5, 0, 1, -bottomMargin),
		AnchorPoint = Vector2.new(0.5, 0),
		BorderSizePixel = 0,
	}, React.createElement("uisizeconstraint", {
		MinSize = Vector2.new(280, 60),
		MaxSize = Vector2.new(500, 100),
	}), React.createElement(ActionBar, {
		slots = abilitySlots,
		onSlotClick = handleSlotClick,
		layoutOrder = props.layoutOrder,
		position = UDim2.new(0.5, 0, 0.5, 0),
		anchorPoint = Vector2.new(0.5, 0.5),
		zIndex = 100,
	})))
end
return {
	ActionBarDemo = ActionBarDemo,
}
