-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local Toast = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "toast", "Toast").Toast
-- Global toast state management
local toastManagerRef
-- Static service for toast notifications
local toastServiceInstance
local ToastService
ToastService = {
	setInstance = function(self, instance)
		toastServiceInstance = instance
	end,
	showToast = function(self, toast)
		if toastServiceInstance then
			toastServiceInstance.addToast(toast)
		else
			warn("ToastManager not initialized")
		end
	end,
	showSuccess = function(self, title, message, duration)
		if duration == nil then
			duration = 3000
		end
		ToastService:showToast({
			title = title,
			message = message,
			type = "success",
			duration = duration,
		})
	end,
	showError = function(self, title, message, duration)
		if duration == nil then
			duration = 5000
		end
		ToastService:showToast({
			title = title,
			message = message,
			type = "error",
			duration = duration,
		})
	end,
	showWarning = function(self, title, message, duration)
		if duration == nil then
			duration = 4000
		end
		ToastService:showToast({
			title = title,
			message = message,
			type = "warning",
			duration = duration,
		})
	end,
	showInfo = function(self, title, message, duration)
		if duration == nil then
			duration = 3000
		end
		ToastService:showToast({
			title = title,
			message = message,
			type = "info",
			duration = duration,
		})
	end,
}
local function ToastManager(props)
	local toasts, setToasts = React.useState({})
	-- Create the manager instance
	local managerInstance = React.useMemo(function()
		return {
			addToast = function(toast)
				local id = `toast-{tick()}-{math.random()}`
				local _object = table.clone(toast)
				setmetatable(_object, nil)
				_object.id = id
				local newToast = _object
				setToasts(function(prev)
					-- Limit to 5 toasts maximum by removing oldest ones
					local maxToasts = 4
					local filtered = prev
					if #prev > maxToasts then
						-- Remove oldest toasts to make room
						filtered = {}
						for i = #prev - maxToasts, #prev - 1 do
							local _filtered = filtered
							local _arg0 = prev[i + 1]
							table.insert(_filtered, _arg0)
						end
					end
					local _array = {}
					local _length = #_array
					local _filteredLength = #filtered
					table.move(filtered, 1, _filteredLength, _length + 1, _array)
					_length += _filteredLength
					_array[_length + 1] = newToast
					return _array
				end)
			end,
		}
	end, {})
	-- Register the instance globally
	React.useEffect(function()
		ToastService:setInstance(managerInstance)
		return function()
			ToastService:setInstance(nil)
		end
	end, { managerInstance })
	local dismissToast = React.useCallback(function(id)
		setToasts(function(prev)
			-- ▼ ReadonlyArray.filter ▼
			local _newValue = {}
			local _callback = function(toast)
				return toast.id ~= id
			end
			local _length = 0
			for _k, _v in prev do
				if _callback(_v, _k - 1, prev) == true then
					_length += 1
					_newValue[_length] = _v
				end
			end
			-- ▲ ReadonlyArray.filter ▲
			return _newValue
		end)
	end, {})
	-- ▼ ReadonlyArray.map ▼
	local _newValue = table.create(#toasts)
	local _callback = function(toast, index)
		return React.createElement(Toast, {
			key = toast.id,
			toast = toast,
			onDismiss = dismissToast,
			index = index,
		})
	end
	for _k, _v in toasts do
		_newValue[_k] = _callback(_v, _k - 1, toasts)
	end
	-- ▲ ReadonlyArray.map ▲
	return React.createElement("frame", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
	}, _newValue)
end
return {
	ToastManager = ToastManager,
	ToastService = ToastService,
}
