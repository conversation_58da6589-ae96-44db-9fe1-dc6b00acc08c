import * as React from "@rbxts/react";
interface ErrorBoundaryState {
    hasError: boolean;
    error?: unknown;
    errorId?: string;
}
interface ErrorBoundaryProps {
    children?: React.ReactNode;
    fallback?: (err: unknown, errorId: string, retry: () => void) => React.ReactElement;
    onError?: (err: unknown, errorId: string) => void;
}
export declare class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
    private errorIdCounter;
    constructor(props: ErrorBoundaryProps);
    static getDerivedStateFromError(err: unknown): ErrorBoundaryState;
    componentDidCatch(err: unknown, errorInfo: React.ErrorInfo): void;
    private handleRetry;
    render(): true | ReadonlyMap<React.Key, React.ReactNode> | {
        readonly [key: string]: React.ReactNode;
        readonly [key: number]: React.ReactNode;
    } | readonly React.ReactNode[] | JSX.Element;
}
export {};
