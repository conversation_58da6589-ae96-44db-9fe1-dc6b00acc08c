import * as React from "@rbxts/react";

interface ErrorBoundaryState {
	hasError: boolean;
	error?: unknown;
	errorId?: string;
}

interface ErrorBoundaryProps {
	children?: React.ReactNode;
	fallback?: (err: unknown, errorId: string, retry: () => void) => React.ReactElement;
	onError?: (err: unknown, errorId: string) => void;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
	private errorIdCounter = 0;

	constructor(props: ErrorBoundaryProps) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(err: unknown): ErrorBoundaryState {
		return { hasError: true, error: err };
	}

	componentDidCatch(err: unknown, errorInfo: React.ErrorInfo) {
		const errorMessage = typeIs(err, "string") ? err : tostring(err);
		const stackTrace = errorInfo.componentStack;

		// Generate error ID safely
		const errorId = `error_boundary_${++this.errorIdCounter}_${tick()}`;

		// Simple logging without dependencies
		warn(`[ErrorBoundary] React component error: ${errorMessage}`);
		warn(`[ErrorBoundary] Component stack: ${stackTrace}`);
		warn(`[ErrorBoundary] Error ID: ${errorId}`);

		this.setState({ errorId });

		// Call custom error handler if provided
		if (this.props.onError) {
			try {
				this.props.onError(err, errorId);
			} catch (handlerError) {
				warn(`[ErrorBoundary] Error handler failed: ${handlerError}`);
			}
		}
	}

	private handleRetry = () => {
		this.setState({ hasError: false, error: undefined, errorId: "" });
	};

	render() {
		if (this.state.hasError) {
			// Use custom fallback if provided
			if (this.props.fallback && this.state.errorId) {
				try {
					return this.props.fallback(this.state.error, this.state.errorId, this.handleRetry);
				} catch (fallbackError) {
					warn(`[ErrorBoundary] Custom fallback failed: ${fallbackError}`);
					// Fall through to default UI
				}
			}

			// Safe default error UI that won't cause more errors
			return (
				<frame
					Size={new UDim2(1, 0, 1, 0)}
					Position={new UDim2(0, 0, 0, 0)}
					BackgroundColor3={Color3.fromHex("#2a2a2a")}
					BackgroundTransparency={0}
					BorderSizePixel={2}
					BorderColor3={Color3.fromHex("#ff4444")}
				>
					<uicorner CornerRadius={new UDim(0, 8)} />
					<uipadding
						PaddingTop={new UDim(0, 16)}
						PaddingBottom={new UDim(0, 16)}
						PaddingLeft={new UDim(0, 16)}
						PaddingRight={new UDim(0, 16)}
					/>
					
					<uilistlayout
						SortOrder={Enum.SortOrder.LayoutOrder}
						FillDirection={Enum.FillDirection.Vertical}
						HorizontalAlignment={Enum.HorizontalAlignment.Center}
						VerticalAlignment={Enum.VerticalAlignment.Center}
						Padding={new UDim(0, 8)}
					/>

					<textlabel
						Text="🚨 Component Error"
						Size={new UDim2(1, 0, 0, 24)}
						BackgroundTransparency={1}
						TextColor3={Color3.fromHex("#ff4444")}
						TextSize={18}
						Font={Enum.Font.SourceSansBold}
						TextXAlignment={Enum.TextXAlignment.Center}
						LayoutOrder={1}
					/>

					<textlabel
						Text="This component encountered an error and has been safely contained."
						Size={new UDim2(1, 0, 0, 40)}
						BackgroundTransparency={1}
						TextColor3={Color3.fromHex("#cccccc")}
						TextSize={14}
						Font={Enum.Font.SourceSans}
						TextXAlignment={Enum.TextXAlignment.Center}
						TextWrapped={true}
						LayoutOrder={2}
					/>

					{this.state.errorId !== "" && this.state.errorId !== undefined && (
						<textlabel
							Text={`Error ID: ${this.state.errorId}`}
							Size={new UDim2(1, 0, 0, 16)}
							BackgroundTransparency={1}
							TextColor3={Color3.fromHex("#888888")}
							TextSize={12}
							Font={Enum.Font.SourceSans}
							TextXAlignment={Enum.TextXAlignment.Center}
							LayoutOrder={3}
						/>
					)}

					<textbutton
						Text="Try Again"
						Size={new UDim2(0, 100, 0, 32)}
						BackgroundColor3={Color3.fromHex("#4a9eff")}
						BorderSizePixel={0}
						TextColor3={Color3.fromHex("#ffffff")}
						TextSize={14}
						Font={Enum.Font.SourceSansBold}
						LayoutOrder={4}
						Event={{
							Activated: this.handleRetry,
						}}
					>
						<uicorner CornerRadius={new UDim(0, 4)} />
					</textbutton>
				</frame>
			);
		}

		return this.props.children || <></>;
	}
}
