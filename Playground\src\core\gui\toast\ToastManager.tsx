import * as React from "@rbxts/react";
import { Toast, ToastData } from "./Toast";

interface ToastManagerProps {
	// Component doesn't need any props as it manages state internally
}

// Global toast state management
let toastManagerRef: ToastManagerInstance | undefined;

interface ToastManagerInstance {
	addToast: (toast: Omit<ToastData, "id">) => void;
}

// Static service for toast notifications
let toastServiceInstance: ToastManagerInstance | undefined;

export const ToastService = {
	setInstance(instance: ToastManagerInstance) {
		toastServiceInstance = instance;
	},

	showToast(toast: Omit<ToastData, "id">) {
		if (toastServiceInstance) {
			toastServiceInstance.addToast(toast);
		} else {
			warn("ToastManager not initialized");
		}
	},

	showSuccess(title: string, message?: string, duration = 3000) {
		ToastService.showToast({
			title,
			message,
			type: "success",
			duration,
		});
	},

	showError(title: string, message?: string, duration = 5000) {
		ToastService.showToast({
			title,
			message,
			type: "error",
			duration,
		});
	},

	showWarning(title: string, message?: string, duration = 4000) {
		ToastService.showToast({
			title,
			message,
			type: "warning",
			duration,
		});
	},

	showInfo(title: string, message?: string, duration = 3000) {
		ToastService.showToast({
			title,
			message,
			type: "info",
			duration,
		});
	},
};

export function ToastManager(props: ToastManagerProps): React.ReactElement {
	const [toasts, setToasts] = React.useState<ToastData[]>([]);

	// Create the manager instance
	const managerInstance = React.useMemo<ToastManagerInstance>(
		() => ({
			addToast: (toast: Omit<ToastData, "id">) => {
				const id = `toast-${tick()}-${math.random()}`;
				const newToast: ToastData = { ...toast, id };

				setToasts((prev) => {
					// Limit to 5 toasts maximum by removing oldest ones
					const maxToasts = 4;
					let filtered = prev;
					if (prev.size() > maxToasts) {
						// Remove oldest toasts to make room
						filtered = [];
						for (let i = prev.size() - maxToasts; i < prev.size(); i++) {
							filtered.push(prev[i]);
						}
					}
					return [...filtered, newToast];
				});
			},
		}),
		[],
	);

	// Register the instance globally
	React.useEffect(() => {
		ToastService.setInstance(managerInstance);
		return () => {
			ToastService.setInstance(undefined);
		};
	}, [managerInstance]);

	const dismissToast = React.useCallback((id: string) => {
		setToasts((prev) => prev.filter((toast) => toast.id !== id));
	}, []);

	return (
		<frame Size={new UDim2(1, 0, 1, 0)} BackgroundTransparency={1} BorderSizePixel={0}>
			{toasts.map((toast, index) => (
				<Toast key={toast.id} toast={toast} onDismiss={dismissToast} index={index} />
			))}
		</frame>
	);
}
