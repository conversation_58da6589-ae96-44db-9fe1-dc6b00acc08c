-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local ErrorBoundary
do
	local super = React.Component
	ErrorBoundary = setmetatable({}, {
		__tostring = function()
			return "ErrorBoundary"
		end,
		__index = super,
	})
	ErrorBoundary.__index = ErrorBoundary
	function ErrorBoundary.new(...)
		local self = setmetatable({}, ErrorBoundary)
		return self:constructor(...) or self
	end
	function ErrorBoundary:constructor(props)
		super.constructor(self, props)
		self.errorIdCounter = 0
		self.handleRetry = function()
			self:setState({
				hasError = false,
				error = nil,
				errorId = "",
			})
		end
		self.state = {
			hasError = false,
		}
	end
	function ErrorBoundary:getDerivedStateFromError(err)
		return {
			hasError = true,
			error = err,
		}
	end
	function ErrorBoundary:componentDidCatch(err, errorInfo)
		local _err = err
		local errorMessage = if type(_err) == "string" then err else tostring(err)
		local stackTrace = errorInfo.componentStack
		-- Generate error ID safely
		self.errorIdCounter += 1
		local errorId = `error_boundary_{self.errorIdCounter}_{tick()}`
		-- Simple logging without dependencies
		warn(`[ErrorBoundary] React component error: {errorMessage}`)
		warn(`[ErrorBoundary] Component stack: {stackTrace}`)
		warn(`[ErrorBoundary] Error ID: {errorId}`)
		self:setState({
			errorId = errorId,
		})
		-- Call custom error handler if provided
		if self.props.onError then
			TS.try(function()
				self.props.onError(err, errorId)
			end, function(handlerError)
				warn(`[ErrorBoundary] Error handler failed: {handlerError}`)
			end)
		end
	end
	function ErrorBoundary:render()
		if self.state.hasError then
			-- Use custom fallback if provided
			local _value = self.props.fallback and self.state.errorId
			if _value ~= "" and _value then
				local _exitType, _returns = TS.try(function()
					return TS.TRY_RETURN, { self.props.fallback(self.state.error, self.state.errorId, self.handleRetry) }
				end, function(fallbackError)
					warn(`[ErrorBoundary] Custom fallback failed: {fallbackError}`)
					-- Fall through to default UI
				end)
				if _exitType then
					return unpack(_returns)
				end
			end
			-- Safe default error UI that won't cause more errors
			return React.createElement("frame", {
				Size = UDim2.new(1, 0, 1, 0),
				Position = UDim2.new(0, 0, 0, 0),
				BackgroundColor3 = Color3.fromHex("#2a2a2a"),
				BackgroundTransparency = 0,
				BorderSizePixel = 2,
				BorderColor3 = Color3.fromHex("#ff4444"),
			}, React.createElement("uicorner", {
				CornerRadius = UDim.new(0, 8),
			}), React.createElement("uipadding", {
				PaddingTop = UDim.new(0, 16),
				PaddingBottom = UDim.new(0, 16),
				PaddingLeft = UDim.new(0, 16),
				PaddingRight = UDim.new(0, 16),
			}), React.createElement("uilistlayout", {
				SortOrder = Enum.SortOrder.LayoutOrder,
				FillDirection = Enum.FillDirection.Vertical,
				HorizontalAlignment = Enum.HorizontalAlignment.Center,
				VerticalAlignment = Enum.VerticalAlignment.Center,
				Padding = UDim.new(0, 8),
			}), React.createElement("textlabel", {
				Text = "🚨 Component Error",
				Size = UDim2.new(1, 0, 0, 24),
				BackgroundTransparency = 1,
				TextColor3 = Color3.fromHex("#ff4444"),
				TextSize = 18,
				Font = Enum.Font.SourceSansBold,
				TextXAlignment = Enum.TextXAlignment.Center,
				LayoutOrder = 1,
			}), React.createElement("textlabel", {
				Text = "This component encountered an error and has been safely contained.",
				Size = UDim2.new(1, 0, 0, 40),
				BackgroundTransparency = 1,
				TextColor3 = Color3.fromHex("#cccccc"),
				TextSize = 14,
				Font = Enum.Font.SourceSans,
				TextXAlignment = Enum.TextXAlignment.Center,
				TextWrapped = true,
				LayoutOrder = 2,
			}), self.state.errorId ~= "" and self.state.errorId ~= nil and (React.createElement("textlabel", {
				Text = `Error ID: {self.state.errorId}`,
				Size = UDim2.new(1, 0, 0, 16),
				BackgroundTransparency = 1,
				TextColor3 = Color3.fromHex("#888888"),
				TextSize = 12,
				Font = Enum.Font.SourceSans,
				TextXAlignment = Enum.TextXAlignment.Center,
				LayoutOrder = 3,
			})), React.createElement("textbutton", {
				Text = "Try Again",
				Size = UDim2.new(0, 100, 0, 32),
				BackgroundColor3 = Color3.fromHex("#4a9eff"),
				BorderSizePixel = 0,
				TextColor3 = Color3.fromHex("#ffffff"),
				TextSize = 14,
				Font = Enum.Font.SourceSansBold,
				LayoutOrder = 4,
				Event = {
					Activated = self.handleRetry,
				},
			}, React.createElement("uicorner", {
				CornerRadius = UDim.new(0, 4),
			})))
		end
		return self.props.children or React.createElement(React.Fragment)
	end
end
return {
	ErrorBoundary = ErrorBoundary,
}
