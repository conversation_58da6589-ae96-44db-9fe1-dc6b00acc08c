import * as React from "@rbxts/react";
import { ToastData } from "./Toast";
interface ToastManagerProps {
}
interface ToastManagerInstance {
    addToast: (toast: Omit<ToastData, "id">) => void;
}
export declare const ToastService: {
    setInstance(instance: ToastManagerInstance): void;
    showToast(toast: Omit<ToastData, "id">): void;
    showSuccess(title: string, message?: string, duration?: number): void;
    showError(title: string, message?: string, duration?: number): void;
    showWarning(title: string, message?: string, duration?: number): void;
    showInfo(title: string, message?: string, duration?: number): void;
};
export declare function ToastManager(props: ToastManagerProps): React.ReactElement;
export {};
